import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Store,
  TrendingUp,
  Package,
  Star,
  Clock,
  DollarSign,
  Eye,
  Plus,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle2,
  Timer,
  Truck,
  Play,
  Pause,
  MapPin,
  Phone,
  ShoppingBag
} from 'lucide-react';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';
import { useOrdersStore } from '../../stores/ordersStore';
import { useNavigate } from 'react-router-dom';

// Import suppliers data from local data directory
import { suppliersData } from '../../data/suppliersData';

const SupplierHomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { orders } = useOrdersStore();

  const [storeOpen, setStoreOpen] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Find supplier data based on user's supplierId
  const supplierData = suppliersData.find((supplier) => supplier.id === user?.supplierId);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Filter orders by status (for now using all orders as mock data)
  const newOrders = orders.filter((o) => o.status === 'Pending');
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing');
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way');
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered');
  const allSupplierOrders = orders; // In real app, filter by supplierId

  // Calculate today's stats
  const today = new Date();
  const todayOrders = allSupplierOrders.filter(order => {
    const orderDate = new Date(order.createdAt || today);
    return orderDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const avgRating = 4.8; // This would come from reviews data
  const totalProducts = 45; // This would come from products data

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Preparing': return "bg-yellow-500";
      case 'On the Way': return "bg-orange-500";
      case 'Delivered': return "bg-green-500";
      default: return "bg-red-500";
    }
  };

  const renderOrderCard = (order: any) => (
    <motion.div
      key={order.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"
    >
      <div className="flex justify-between items-center mb-3">
        <span className="text-gray-900 font-bold text-sm">#{order.id}</span>
        <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2 text-gray-600">
          <Package size={16} />
          <span className="text-sm">{order.items.length} items</span>
        </div>
        <div className="flex items-center gap-2 text-gray-600">
          <DollarSign size={16} />
          <span className="text-sm">₪{order.total.toFixed(2)}</span>
        </div>
        <div className="flex items-center gap-2 text-gray-600">
          <MapPin size={16} />
          <span className="text-sm truncate">{order.address || 'No address'}</span>
        </div>
      </div>

      <button
        onClick={() => navigate(`/supplier/order-details/${order.id}`)}
        className="w-full mt-3 px-4 py-2 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors duration-200 flex items-center justify-center gap-2"
      >
        <Eye size={16} />
        View
      </button>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4 pb-20">
      <div className="max-w-6xl mx-auto space-y-6">

        {/* EXTREME Dashboard Header */}
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          className="relative overflow-hidden rounded-2xl shadow-2xl"
        >
          <div className="bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 p-8">
            {/* Decorative Background Elements */}
            <div className="absolute -top-16 -right-16 w-36 h-36 bg-white/10 rounded-full"></div>
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-white/5 rounded-full"></div>

            <div className="relative z-10 space-y-6">
              {/* Welcome Section */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <div className="bg-white/20 backdrop-blur-sm border-2 border-white/20 rounded-2xl p-5">
                      <Store size={40} className="text-white" />
                    </div>

                    {/* Store Status Indicator */}
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.6, type: 'spring', damping: 10 }}
                      className="absolute -top-2 -right-2"
                    >
                      <div className={`${storeOpen ? 'bg-green-500' : 'bg-red-500'} border-2 border-white rounded-xl p-1.5`}>
                        {storeOpen ? <CheckCircle2 size={12} className="text-white" /> : <AlertCircle size={12} className="text-white" />}
                      </div>
                    </motion.div>
                  </motion.div>

                  <div className="flex-1">
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.6 }}
                      className="text-white text-3xl font-black"
                    >
                      👋 Hello, {supplierData?.name || 'Supplier'}
                    </motion.h1>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.6 }}
                      className="flex items-center gap-3 mt-2"
                    >
                      <div className={`${storeOpen ? 'bg-green-500' : 'bg-red-500'} px-3 py-1 rounded-lg`}>
                        <span className="text-white text-sm font-bold">
                          {storeOpen ? '🟢 OPEN' : '🔴 CLOSED'}
                        </span>
                      </div>
                      <span className="text-white/90 text-lg font-medium">
                        {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </motion.div>
                  </div>
                </div>

                {/* Quick Store Toggle */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8, duration: 0.4 }}
                  onClick={() => setStoreOpen(!storeOpen)}
                  className="bg-white/15 backdrop-blur-sm border-2 border-white/30 rounded-2xl p-4 hover:bg-white/20 transition-all duration-200"
                >
                  {storeOpen ? <Pause size={24} className="text-white" /> : <Play size={24} className="text-white" />}
                </motion.button>
              </div>

              {/* Quick Stats Grid */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9, duration: 0.6 }}
                className="grid grid-cols-3 gap-4"
              >
                <div className="text-center">
                  <p className="text-white/80 text-sm font-semibold">TODAY'S REVENUE</p>
                  <p className="text-white text-2xl font-black">₪{todayRevenue.toFixed(0)}</p>
                </div>

                <div className="border-l border-r border-white/30 text-center">
                  <p className="text-white/80 text-sm font-semibold">TODAY'S ORDERS</p>
                  <p className="text-white text-2xl font-black">{todayOrders.length}</p>
                </div>

                <div className="text-center">
                  <p className="text-white/80 text-sm font-semibold">RATING</p>
                  <div className="flex items-center justify-center gap-1">
                    <Star size={16} className="text-yellow-400 fill-current" />
                    <p className="text-white text-2xl font-black">{avgRating}</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200"
        >
          <h3 className="text-gray-900 text-xl font-black mb-4 flex items-center gap-2">
            ⚡ Quick Actions
          </h3>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <motion.button
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.4, type: 'spring', damping: 15 }}
              onClick={() => navigate('/supplier/products/add')}
              className="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-5 text-white hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <Plus size={32} className="mx-auto mb-2" />
              <p className="font-bold text-sm">Add Product</p>
            </motion.button>

            <motion.button
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, type: 'spring', damping: 15 }}
              onClick={() => navigate('/supplier/analytics')}
              className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-5 text-white hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <BarChart3 size={32} className="mx-auto mb-2" />
              <p className="font-bold text-sm">Analytics</p>
            </motion.button>

            <motion.button
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.6, type: 'spring', damping: 15 }}
              onClick={() => navigate('/supplier/products')}
              className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl p-5 text-white hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <ShoppingBag size={32} className="mx-auto mb-2" />
              <p className="font-bold text-sm">Products</p>
            </motion.button>

            <motion.button
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.7, type: 'spring', damping: 15 }}
              onClick={() => navigate('/supplier/profile')}
              className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-5 text-white hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <Settings size={32} className="mx-auto mb-2" />
              <p className="font-bold text-sm">Settings</p>
            </motion.button>
          </div>
        </motion.div>

        {/* Orders Section - PROMINENT & ENHANCED */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="relative overflow-hidden rounded-2xl shadow-2xl"
        >
          <div className="bg-gradient-to-br from-red-500 via-red-600 to-red-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-3">
                  <AlertCircle size={28} className="text-white" />
                </div>
                <div>
                  <h2 className="text-white text-2xl font-black">🚨 New Orders</h2>
                  <p className="text-white/90 text-sm">{newOrders.length} orders need attention</p>
                </div>
              </div>

              {newOrders.length > 0 && (
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2">
                  <span className="text-white text-xl font-black">{newOrders.length}</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white p-6">
            {newOrders.length > 0 ? (
              <div className="space-y-4">
                {newOrders.slice(0, 3).map((order, index) => (
                  <motion.div
                    key={order.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + index * 0.1, duration: 0.4 }}
                  >
                    {renderOrderCard(order)}
                  </motion.div>
                ))}
                {newOrders.length > 3 && (
                  <button className="w-full bg-red-600 text-white py-3 rounded-xl font-bold hover:bg-red-700 transition-colors duration-200 flex items-center justify-center gap-2">
                    View All {newOrders.length} New Orders
                    <Eye size={20} />
                  </button>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="bg-gray-100 rounded-2xl p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                  <CheckCircle2 size={32} className="text-green-500" />
                </div>
                <p className="text-gray-600 font-semibold">🎉 All caught up! No new orders.</p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Performance Highlights */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200"
        >
          <h3 className="text-gray-900 text-xl font-black mb-4 flex items-center gap-2">
            📊 Today's Highlights
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Revenue Card */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.6, type: 'spring', damping: 15 }}
              className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-4 text-white"
            >
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp size={20} />
                <span className="text-sm font-semibold opacity-90">REVENUE</span>
              </div>
              <p className="text-2xl font-black">₪{todayRevenue.toFixed(0)}</p>
              <p className="text-xs opacity-80">+12% from yesterday</p>
            </motion.div>

            {/* Orders Card */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.7, type: 'spring', damping: 15 }}
              className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4 text-white"
            >
              <div className="flex items-center gap-2 mb-2">
                <Package size={20} />
                <span className="text-sm font-semibold opacity-90">ORDERS</span>
              </div>
              <p className="text-2xl font-black">{todayOrders.length}</p>
              <p className="text-xs opacity-80">
                {todayOrders.length > 0 ? `+${todayOrders.length} today` : `${todayOrders.length} today`}
              </p>
            </motion.div>
          </div>

          {/* Quick Stats Row */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-gray-500 text-xs font-semibold">AVG ORDER</p>
              <p className="text-gray-900 text-lg font-black">
                ₪{todayOrders.length > 0 ? (todayRevenue / todayOrders.length).toFixed(0) : '0'}
              </p>
            </div>

            <div className="border-l border-r border-gray-200">
              <p className="text-gray-500 text-xs font-semibold">COMPLETION</p>
              <p className="text-gray-900 text-lg font-black">
                {allSupplierOrders.length > 0
                  ? Math.round((deliveredOrders.length / allSupplierOrders.length) * 100)
                  : 0}%
              </p>
            </div>

            <div>
              <p className="text-gray-500 text-xs font-semibold">PRODUCTS</p>
              <p className="text-gray-900 text-lg font-black">{totalProducts}</p>
            </div>
          </div>
        </motion.div>

        {/* Enhanced Other Order Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <EnhancedSection
            title="In Preparing Orders"
            icon={Timer}
            color="#f59e0b"
            orders={inPreparingOrders}
            bgGradient="from-yellow-500 to-orange-500"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <EnhancedSection
            title="On The Way Orders"
            icon={Truck}
            color="#f97316"
            orders={onTheWayOrders}
            bgGradient="from-orange-500 to-red-500"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <EnhancedSection
            title="Delivered Orders"
            icon={CheckCircle2}
            color="#10b981"
            orders={deliveredOrders}
            bgGradient="from-green-500 to-emerald-600"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>
      </div>
    </div>
  );
};

// Enhanced Section Component
const EnhancedSection: React.FC<{
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  orders: any[];
  bgGradient: string;
  renderOrderCard: (order: any) => React.ReactNode;
}> = ({ title, icon: Icon, color, orders, bgGradient, renderOrderCard }) => (
  <div className="relative overflow-hidden rounded-2xl shadow-xl border-0">
    <div className={`bg-gradient-to-br ${bgGradient} p-6`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-3">
            <Icon size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-white text-xl font-black">{title}</h3>
            <p className="text-white/90 text-sm">{orders.length} orders</p>
          </div>
        </div>

        {orders.length > 0 && (
          <div className="bg-white/20 backdrop-blur-sm rounded-2xl px-3 py-1.5">
            <span className="text-white text-lg font-black">{orders.length}</span>
          </div>
        )}
      </div>
    </div>

    <div className="bg-white p-6">
      {orders.length > 0 ? (
        <div className="space-y-3">
          {orders.slice(0, 2).map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1, duration: 0.4 }}
            >
              {renderOrderCard(order)}
            </motion.div>
          ))}
          {orders.length > 2 && (
            <button
              style={{ backgroundColor: color }}
              className="w-full text-white py-3 rounded-xl font-semibold hover:opacity-90 transition-opacity duration-200 flex items-center justify-center gap-2"
            >
              View All {orders.length} Orders
              <Eye size={16} />
            </button>
          )}
        </div>
      ) : (
        <div className="text-center py-6">
          <div className="bg-gray-100 rounded-2xl p-3 w-12 h-12 mx-auto mb-2 flex items-center justify-center">
            <CheckCircle2 size={24} className="text-green-500" />
          </div>
          <p className="text-gray-600 text-sm font-semibold">No {title.toLowerCase()}</p>
        </div>
      )}
    </div>
  </div>
);

export default SupplierHomePage;
