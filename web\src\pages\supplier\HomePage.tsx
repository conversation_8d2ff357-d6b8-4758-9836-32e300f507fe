import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Store,
  TrendingUp,
  Package,
  Star,
  Clock,
  DollarSign,
  Eye,
  Plus,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle2,
  Timer,
  Truck,
  Play,
  Pause,
  MapPin,
  Phone,
  ShoppingBag,
  Zap,
  Award,
  Target,
  Users,
  Calendar,
  Activity,
  Sparkles,
  Crown,
  Flame,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  TrendingDown
} from 'lucide-react';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';
import { useOrdersStore } from '../../stores/ordersStore';
import { useNavigate } from 'react-router-dom';

// Import suppliers data from local data directory
import { suppliersData } from '../../data/suppliersData';

const SupplierHomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { orders } = useOrdersStore();

  const [storeOpen, setStoreOpen] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Find supplier data based on user's supplierId
  const supplierData = suppliersData.find((supplier) => supplier.id === user?.supplierId);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Filter orders by status (for now using all orders as mock data)
  const newOrders = orders.filter((o) => o.status === 'Pending');
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing');
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way');
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered');
  const allSupplierOrders = orders; // In real app, filter by supplierId

  // Calculate today's stats
  const today = new Date();
  const todayOrders = allSupplierOrders.filter(order => {
    const orderDate = new Date(order.createdAt || today);
    return orderDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const avgRating = 4.8; // This would come from reviews data
  const totalProducts = 45; // This would come from products data

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Preparing': return "bg-yellow-500";
      case 'On the Way': return "bg-orange-500";
      case 'Delivered': return "bg-green-500";
      default: return "bg-red-500";
    }
  };

  const renderOrderCard = (order: any) => (
    <motion.div
      key={order.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02, y: -2 }}
      className="group relative bg-white rounded-2xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
    >
      {/* Background Gradient on Hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-blue-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <div className="relative z-10">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl">
              <Package size={16} className="text-purple-600" />
            </div>
            <span className="text-gray-900 font-bold text-lg">#{order.id}</span>
          </div>
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={`px-3 py-1.5 rounded-xl text-xs font-bold text-white shadow-md ${getStatusColor(order.status)}`}
          >
            {order.status}
          </motion.span>
        </div>

        <div className="space-y-3 mb-4">
          <div className="flex items-center gap-3 text-gray-700">
            <div className="p-1.5 bg-blue-100 rounded-lg">
              <Package size={14} className="text-blue-600" />
            </div>
            <span className="text-sm font-medium">{order.items.length} items</span>
          </div>
          <div className="flex items-center gap-3 text-gray-700">
            <div className="p-1.5 bg-green-100 rounded-lg">
              <DollarSign size={14} className="text-green-600" />
            </div>
            <span className="text-sm font-medium">₪{order.total.toFixed(2)}</span>
          </div>
          <div className="flex items-center gap-3 text-gray-700">
            <div className="p-1.5 bg-orange-100 rounded-lg">
              <MapPin size={14} className="text-orange-600" />
            </div>
            <span className="text-sm font-medium truncate">{order.address || 'No address'}</span>
          </div>
          <div className="flex items-center gap-3 text-gray-700">
            <div className="p-1.5 bg-purple-100 rounded-lg">
              <Phone size={14} className="text-purple-600" />
            </div>
            <span className="text-sm font-medium">{order.phone}</span>
          </div>
        </div>

        <motion.button
          onClick={() => navigate(`/supplier/order-details/${order.id}`)}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center gap-2 font-semibold shadow-lg"
        >
          <Eye size={18} />
          View Details
          <ChevronRight size={16} />
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <>
      {/* CSS Animations */}
      <style>{`
        @keyframes slide {
          0% { transform: translateX(-20px); }
          100% { transform: translateX(20px); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
          50% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.6); }
        }
      `}</style>

      <div className="min-h-screen bg-gray-50 p-4 pb-20">
        <div className="max-w-6xl mx-auto space-y-6">

        {/* EXTREME Dashboard Header */}
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          className="relative overflow-hidden rounded-3xl shadow-2xl"
        >
          {/* Animated Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse"></div>
          </div>

          {/* Floating Particles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-white/20 rounded-full"
                initial={{
                  x: Math.random() * 100 + '%',
                  y: Math.random() * 100 + '%',
                  scale: 0
                }}
                animate={{
                  y: [null, '-100%'],
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: Math.random() * 3 + 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>

          {/* Decorative Background Elements */}
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-white/10 to-white/5 rounded-full blur-xl"></div>
          <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-tr from-white/8 to-white/3 rounded-full blur-lg"></div>
          <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-white/5 rounded-full blur-md"></div>

          <div className="relative z-10 space-y-8">
              {/* Welcome Section */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                    className="relative group"
                  >
                    {/* Glowing Ring Effect */}
                    <div className="absolute -inset-2 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-500 rounded-full opacity-75 group-hover:opacity-100 blur-sm animate-pulse"></div>

                    <div className="relative bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-3xl p-6 shadow-2xl">
                      <Store size={48} className="text-white drop-shadow-lg" />

                      {/* Sparkle Effects */}
                      <motion.div
                        className="absolute -top-1 -right-1 text-yellow-300"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Sparkles size={16} />
                      </motion.div>
                    </div>

                    {/* Store Status Indicator with Pulse */}
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.6, type: 'spring', damping: 10 }}
                      className="absolute -top-3 -right-3"
                    >
                      <div className={`${storeOpen ? 'bg-green-500' : 'bg-red-500'} border-3 border-white rounded-2xl p-2 shadow-lg`}>
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {storeOpen ? <CheckCircle2 size={16} className="text-white" /> : <AlertCircle size={16} className="text-white" />}
                        </motion.div>
                      </div>

                      {/* Pulse Ring */}
                      <div className={`absolute inset-0 ${storeOpen ? 'bg-green-400' : 'bg-red-400'} rounded-2xl animate-ping opacity-20`}></div>
                    </motion.div>
                  </motion.div>

                  <div className="flex-1 space-y-4">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6, duration: 0.6 }}
                    >
                      <h1 className="text-white text-4xl font-black mb-2 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                        👋 Hello, {supplierData?.name || 'Supplier'}
                      </h1>
                      <div className="flex items-center gap-2">
                        <Crown size={20} className="text-yellow-400" />
                        <span className="text-yellow-200 text-sm font-semibold">Premium Supplier</span>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7, duration: 0.6 }}
                      className="flex items-center gap-4"
                    >
                      <motion.div
                        className={`${storeOpen ? 'bg-green-500' : 'bg-red-500'} px-4 py-2 rounded-xl shadow-lg border border-white/20`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="text-white text-sm font-bold flex items-center gap-2">
                          {storeOpen ? (
                            <>
                              <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                              🟢 OPEN
                            </>
                          ) : (
                            <>
                              <div className="w-2 h-2 bg-red-300 rounded-full animate-pulse"></div>
                              🔴 CLOSED
                            </>
                          )}
                        </span>
                      </motion.div>

                      <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg">
                        <Clock size={16} className="text-white/80" />
                        <span className="text-white/90 text-lg font-medium">
                          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Store Toggle */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8, duration: 0.4 }}
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <button
                    onClick={() => setStoreOpen(!storeOpen)}
                    className="group relative bg-white/15 backdrop-blur-sm border-2 border-white/30 rounded-3xl p-5 hover:bg-white/20 transition-all duration-300 shadow-2xl"
                  >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    {storeOpen ? (
                      <Pause size={28} className="text-white drop-shadow-lg" />
                    ) : (
                      <Play size={28} className="text-white drop-shadow-lg" />
                    )}
                  </div>

                  {/* Tooltip */}
                  <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-3 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                    {storeOpen ? 'Close Store' : 'Open Store'}
                  </div>
                  </button>
                </motion.div>
              </div>

              {/* Enhanced Stats Grid */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9, duration: 0.6 }}
                className="grid grid-cols-3 gap-6"
              >
                {/* Revenue Stat */}
                <motion.div
                  className="text-center group cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 group-hover:bg-white/15 transition-all duration-300">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <TrendingUp size={20} className="text-green-400" />
                      <p className="text-white/80 text-sm font-semibold">TODAY'S REVENUE</p>
                    </div>
                    <p className="text-white text-3xl font-black mb-1">₪{todayRevenue.toFixed(0)}</p>
                    <div className="flex items-center justify-center gap-1 text-green-400 text-xs">
                      <ArrowUp size={12} />
                      <span>+12% vs yesterday</span>
                    </div>
                  </div>
                </motion.div>

                {/* Orders Stat */}
                <motion.div
                  className="text-center group cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 group-hover:bg-white/15 transition-all duration-300">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Package size={20} className="text-blue-400" />
                      <p className="text-white/80 text-sm font-semibold">TODAY'S ORDERS</p>
                    </div>
                    <p className="text-white text-3xl font-black mb-1">{todayOrders.length}</p>
                    <div className="flex items-center justify-center gap-1 text-blue-400 text-xs">
                      <Activity size={12} />
                      <span>{newOrders.length} pending</span>
                    </div>
                  </div>
                </motion.div>

                {/* Rating Stat */}
                <motion.div
                  className="text-center group cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 group-hover:bg-white/15 transition-all duration-300">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Award size={20} className="text-yellow-400" />
                      <p className="text-white/80 text-sm font-semibold">RATING</p>
                    </div>
                    <div className="flex items-center justify-center gap-2 mb-1">
                      <Star size={20} className="text-yellow-400 fill-current" />
                      <p className="text-white text-3xl font-black">{avgRating}</p>
                    </div>
                    <div className="flex items-center justify-center gap-1 text-yellow-400 text-xs">
                      <Target size={12} />
                      <span>Excellent</span>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
          </div>
        </motion.div>

        {/* Enhanced Quick Actions Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="relative bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25px 25px, #7c3aed 2px, transparent 0)`,
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="relative z-10">
            <motion.h3
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-gray-900 text-2xl font-black mb-6 flex items-center gap-3"
            >
              <div className="p-2 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl">
                <Zap size={24} className="text-white" />
              </div>
              Quick Actions
              <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
            </motion.h3>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Add Product Action */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                transition={{ delay: 0.5, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/supplier/products/add')}
                className="group cursor-pointer relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl p-6 text-white shadow-xl border border-green-400/20">
                  <div className="absolute top-2 right-2 opacity-20">
                    <Sparkles size={20} />
                  </div>
                  <Plus size={36} className="mx-auto mb-3 drop-shadow-lg" />
                  <p className="font-bold text-sm text-center">Add Product</p>
                  <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </motion.div>

              {/* Analytics Action */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                transition={{ delay: 0.6, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/supplier/analytics')}
                className="group cursor-pointer relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl p-6 text-white shadow-xl border border-blue-400/20">
                  <div className="absolute top-2 right-2 opacity-20">
                    <Activity size={20} />
                  </div>
                  <BarChart3 size={36} className="mx-auto mb-3 drop-shadow-lg" />
                  <p className="font-bold text-sm text-center">Analytics</p>
                  <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </motion.div>

              {/* Products Action */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                transition={{ delay: 0.7, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/supplier/products')}
                className="group cursor-pointer relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-orange-400 to-red-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl p-6 text-white shadow-xl border border-orange-400/20">
                  <div className="absolute top-2 right-2 opacity-20">
                    <Package size={20} />
                  </div>
                  <ShoppingBag size={36} className="mx-auto mb-3 drop-shadow-lg" />
                  <p className="font-bold text-sm text-center">Products</p>
                  <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </motion.div>

              {/* Settings Action */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                transition={{ delay: 0.8, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/supplier/profile')}
                className="group cursor-pointer relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-400 to-pink-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl p-6 text-white shadow-xl border border-purple-400/20">
                  <div className="absolute top-2 right-2 opacity-20">
                    <Crown size={20} />
                  </div>
                  <Settings size={36} className="mx-auto mb-3 drop-shadow-lg" />
                  <p className="font-bold text-sm text-center">Settings</p>
                  <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* EXTREME New Orders Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="relative overflow-hidden rounded-3xl shadow-2xl"
        >
          {/* Animated Alert Background */}
          <div className="relative bg-gradient-to-br from-red-500 via-red-600 to-red-700 p-8">
            {/* Pulsing Alert Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 via-transparent to-red-400/20 animate-pulse"></div>

            {/* Danger Stripes Animation */}
            <div className="absolute inset-0 opacity-10">
              <div className="h-full w-full" style={{
                backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px)`,
                animation: 'slide 2s linear infinite'
              }}></div>
            </div>

            <div className="relative z-10 flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="relative"
                >
                  <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-4 border border-white/30">
                    <AlertCircle size={32} className="text-white drop-shadow-lg" />
                  </div>

                  {/* Danger Pulse */}
                  <div className="absolute inset-0 bg-red-400 rounded-3xl animate-ping opacity-20"></div>
                </motion.div>

                <div>
                  <motion.h2
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-white text-3xl font-black mb-1 flex items-center gap-2"
                  >
                    🚨 URGENT: New Orders
                    <motion.div
                      animate={{ opacity: [1, 0.5, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      <Flame size={28} className="text-orange-300" />
                    </motion.div>
                  </motion.h2>
                  <p className="text-white/90 text-lg font-semibold">
                    {newOrders.length} orders require immediate attention
                  </p>
                </div>
              </div>

              {newOrders.length > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, type: 'spring', damping: 10 }}
                  className="relative"
                >
                  <div className="bg-white/20 backdrop-blur-sm rounded-3xl px-6 py-3 border border-white/30">
                    <span className="text-white text-2xl font-black">{newOrders.length}</span>
                  </div>
                  <div className="absolute inset-0 bg-white/10 rounded-3xl animate-pulse"></div>
                </motion.div>
              )}
            </div>
          </div>

          <div className="bg-white p-8">
            <AnimatePresence>
              {newOrders.length > 0 ? (
                <div className="space-y-6">
                  {newOrders.slice(0, 3).map((order, index) => (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, x: -30, scale: 0.9 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: 30, scale: 0.9 }}
                      transition={{
                        delay: 0.5 + index * 0.1,
                        duration: 0.4,
                        type: 'spring',
                        damping: 15
                      }}
                      whileHover={{ scale: 1.02, y: -2 }}
                    >
                      {renderOrderCard(order)}
                    </motion.div>
                  ))}
                  {newOrders.length > 3 && (
                    <motion.button
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-4 rounded-2xl font-bold hover:from-red-700 hover:to-red-800 transition-all duration-200 flex items-center justify-center gap-3 shadow-lg"
                    >
                      View All {newOrders.length} New Orders
                      <ChevronRight size={20} />
                    </motion.button>
                  )}
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-12"
                >
                  <div className="bg-gradient-to-br from-green-100 to-emerald-100 rounded-3xl p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                    <CheckCircle2 size={40} className="text-green-500" />
                  </div>
                  <h3 className="text-gray-800 text-xl font-bold mb-2">🎉 All Caught Up!</h3>
                  <p className="text-gray-600 font-medium">No new orders at the moment. Great job!</p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Enhanced Performance Highlights */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="relative bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 overflow-hidden"
        >
          {/* Background Decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-100 to-yellow-100 rounded-full translate-y-12 -translate-x-12 opacity-50"></div>

          <div className="relative z-10">
            <motion.h3
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="text-gray-900 text-2xl font-black mb-8 flex items-center gap-3"
            >
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                <BarChart3 size={24} className="text-white" />
              </div>
              Today's Performance
              <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
            </motion.h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Enhanced Revenue Card */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0, x: -20 }}
                animate={{ scale: 1, opacity: 1, x: 0 }}
                transition={{ delay: 0.7, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.02, y: -2 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl p-6 text-white shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <TrendingUp size={24} className="drop-shadow-lg" />
                      <span className="text-sm font-bold opacity-90">REVENUE</span>
                    </div>
                    <div className="flex items-center gap-1 text-green-200 text-xs">
                      <ArrowUp size={12} />
                      <span>+12%</span>
                    </div>
                  </div>
                  <p className="text-3xl font-black mb-2">₪{todayRevenue.toFixed(0)}</p>
                  <p className="text-sm opacity-80">vs yesterday: ₪{(todayRevenue * 0.88).toFixed(0)}</p>
                  <div className="absolute top-2 right-2 opacity-20">
                    <DollarSign size={32} />
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Orders Card */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0, x: 20 }}
                animate={{ scale: 1, opacity: 1, x: 0 }}
                transition={{ delay: 0.8, type: 'spring', damping: 15 }}
                whileHover={{ scale: 1.02, y: -2 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl p-6 text-white shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Package size={24} className="drop-shadow-lg" />
                      <span className="text-sm font-bold opacity-90">ORDERS</span>
                    </div>
                    <div className="flex items-center gap-1 text-blue-200 text-xs">
                      <Activity size={12} />
                      <span>{newOrders.length} pending</span>
                    </div>
                  </div>
                  <p className="text-3xl font-black mb-2">{todayOrders.length}</p>
                  <p className="text-sm opacity-80">
                    {todayOrders.length > 0 ? `+${todayOrders.length} today` : `${todayOrders.length} today`}
                  </p>
                  <div className="absolute top-2 right-2 opacity-20">
                    <ShoppingBag size={32} />
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Enhanced Quick Stats Row */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="grid grid-cols-3 gap-6"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="text-center p-4 bg-gray-50 rounded-2xl border border-gray-100 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Target size={16} className="text-orange-500" />
                  <p className="text-gray-600 text-xs font-bold">AVG ORDER</p>
                </div>
                <p className="text-gray-900 text-xl font-black">
                  ₪{todayOrders.length > 0 ? (todayRevenue / todayOrders.length).toFixed(0) : '0'}
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="text-center p-4 bg-gray-50 rounded-2xl border border-gray-100 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-center gap-2 mb-2">
                  <CheckCircle2 size={16} className="text-green-500" />
                  <p className="text-gray-600 text-xs font-bold">COMPLETION</p>
                </div>
                <p className="text-gray-900 text-xl font-black">
                  {allSupplierOrders.length > 0
                    ? Math.round((deliveredOrders.length / allSupplierOrders.length) * 100)
                    : 0}%
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="text-center p-4 bg-gray-50 rounded-2xl border border-gray-100 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Package size={16} className="text-purple-500" />
                  <p className="text-gray-600 text-xs font-bold">PRODUCTS</p>
                </div>
                <p className="text-gray-900 text-xl font-black">{totalProducts}</p>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Enhanced Other Order Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <EnhancedSection
            title="In Preparing Orders"
            icon={Timer}
            color="#f59e0b"
            orders={inPreparingOrders}
            bgGradient="from-yellow-500 to-orange-500"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <EnhancedSection
            title="On The Way Orders"
            icon={Truck}
            color="#f97316"
            orders={onTheWayOrders}
            bgGradient="from-orange-500 to-red-500"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <EnhancedSection
            title="Delivered Orders"
            icon={CheckCircle2}
            color="#10b981"
            orders={deliveredOrders}
            bgGradient="from-green-500 to-emerald-600"
            renderOrderCard={renderOrderCard}
          />
        </motion.div>
      </div>
    </div>
    </>
  );
};

// Enhanced Section Component with Extreme Design
const EnhancedSection: React.FC<{
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  orders: any[];
  bgGradient: string;
  renderOrderCard: (order: any) => React.ReactNode;
}> = ({ title, icon: Icon, color, orders, bgGradient, renderOrderCard }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    whileHover={{ scale: 1.01 }}
    className="relative overflow-hidden rounded-3xl shadow-2xl border-0"
  >
    {/* Enhanced Header with Animations */}
    <div className={`relative bg-gradient-to-br ${bgGradient} p-8`}>
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="h-full w-full" style={{
          backgroundImage: `radial-gradient(circle at 20% 50%, rgba(255,255,255,0.2) 2px, transparent 2px)`,
          backgroundSize: '30px 30px',
          animation: 'float 6s ease-in-out infinite'
        }}></div>
      </div>

      <div className="relative z-10 flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <motion.div
            whileHover={{ rotate: 360, scale: 1.1 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-4 border border-white/30 shadow-xl">
              <Icon size={28} className="text-white drop-shadow-lg" />
            </div>
            <div className="absolute inset-0 bg-white/10 rounded-2xl animate-pulse"></div>
          </motion.div>

          <div>
            <motion.h3
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="text-white text-2xl font-black mb-1"
            >
              {title}
            </motion.h3>
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="text-white/90 text-sm font-semibold flex items-center gap-2"
            >
              <Activity size={14} />
              {orders.length} orders
            </motion.p>
          </div>
        </div>

        <AnimatePresence>
          {orders.length > 0 && (
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              className="relative"
            >
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl px-5 py-3 border border-white/30 shadow-xl">
                <span className="text-white text-xl font-black">{orders.length}</span>
              </div>
              <motion.div
                className="absolute inset-0 bg-white/10 rounded-3xl"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              ></motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>

    {/* Enhanced Content Area */}
    <div className="bg-white p-8">
      <AnimatePresence>
        {orders.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-6"
          >
            {orders.slice(0, 2).map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, x: -30, scale: 0.9 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                transition={{
                  delay: index * 0.1,
                  duration: 0.4,
                  type: 'spring',
                  damping: 15
                }}
              >
                {renderOrderCard(order)}
              </motion.div>
            ))}
            {orders.length > 2 && (
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                style={{ backgroundColor: color }}
                className="w-full text-white py-4 rounded-2xl font-bold hover:opacity-90 transition-all duration-200 flex items-center justify-center gap-3 shadow-lg"
              >
                View All {orders.length} Orders
                <ChevronRight size={18} />
              </motion.button>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl p-6 w-20 h-20 mx-auto mb-4 flex items-center justify-center shadow-lg"
            >
              <CheckCircle2 size={32} className="text-green-500" />
            </motion.div>
            <h4 className="text-gray-800 text-lg font-bold mb-2">All Clear!</h4>
            <p className="text-gray-600 text-sm font-medium">No {title.toLowerCase()} at the moment</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>

    {/* Floating Action Indicator */}
    {orders.length > 0 && (
      <motion.div
        className="absolute top-4 right-4 w-3 h-3 bg-yellow-400 rounded-full"
        animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
    )}
  </motion.div>
);

export default SupplierHomePage;
